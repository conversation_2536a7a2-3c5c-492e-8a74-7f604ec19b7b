import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, test, vi } from 'vitest';
import Steps from '@/components/core/components/steps';
import type { StepItem } from '@/components/core/components/steps';

describe('Steps', () => {
  const mockItems: StepItem[] = [
    {
      title: '步骤一',
      description: '这是第一步',
    },
    {
      title: '步骤二',
      description: '这是第二步',
    },
    {
      title: '步骤三',
      description: '这是第三步',
    },
  ];

  test('renders basic steps correctly', () => {
    render(<Steps items={mockItems} current={1} />);

    expect(screen.getByText('步骤一')).toBeInTheDocument();
    expect(screen.getByText('步骤二')).toBeInTheDocument();
    expect(screen.getByText('步骤三')).toBeInTheDocument();
    expect(screen.getByText('这是第一步')).toBeInTheDocument();
    expect(screen.getByText('这是第二步')).toBeInTheDocument();
    expect(screen.getByText('这是第三步')).toBeInTheDocument();
  });

  test('renders with correct current step', () => {
    const { container } = render(<Steps items={mockItems} current={1} />);

    // 第一步应该是完成状态（绿色），显示勾选图标
    const firstStepIcon = container
      .querySelector('[aria-label="完成"]')
      ?.closest('div');
    expect(firstStepIcon).toHaveClass('bg-green-500');

    // 第二步应该是进行中状态（蓝色），显示数字
    const secondStep = screen.getByText('2').closest('div');
    expect(secondStep).toHaveClass('bg-blue-500');

    // 第三步应该是等待状态（灰色），显示数字
    const thirdStep = screen.getByText('3').closest('div');
    expect(thirdStep).toHaveClass('bg-white');
  });

  test('renders vertical direction correctly', () => {
    const { container } = render(
      <Steps items={mockItems} current={1} direction="vertical" />,
    );

    const stepsContainer = container.firstChild as HTMLElement;
    expect(stepsContainer).toHaveClass('flex-col');
  });

  test('renders horizontal direction correctly', () => {
    const { container } = render(
      <Steps items={mockItems} current={1} direction="horizontal" />,
    );

    const stepsContainer = container.firstChild as HTMLElement;
    expect(stepsContainer).toHaveClass('flex-row');
  });

  test('handles clickable steps', () => {
    const onChange = vi.fn();
    render(
      <Steps items={mockItems} current={1} clickable onChange={onChange} />,
    );

    const firstStepTitle = screen.getByText('步骤一');
    const stepItem = firstStepTitle.closest('div[class*="cursor-pointer"]');

    expect(stepItem).toHaveClass('cursor-pointer');

    fireEvent.click(stepItem!);
    expect(onChange).toHaveBeenCalledWith(0);
  });

  test('does not trigger click when not clickable', () => {
    const onChange = vi.fn();
    render(
      <Steps
        items={mockItems}
        current={1}
        clickable={false}
        onChange={onChange}
      />,
    );

    const firstStepTitle = screen.getByText('步骤一');
    const stepItem = firstStepTitle.closest('div');

    fireEvent.click(stepItem!);
    expect(onChange).not.toHaveBeenCalled();
  });

  test('renders custom status correctly', () => {
    const customItems: StepItem[] = [
      {
        title: '错误步骤',
        status: 'error',
      },
      {
        title: '完成步骤',
        status: 'finish',
      },
    ];

    const { container } = render(<Steps items={customItems} />);

    // 错误状态应该显示红色
    const errorIcon = container
      .querySelector('[aria-label="错误"]')
      ?.closest('div');
    expect(errorIcon).toHaveClass('bg-red-500');

    // 完成状态应该显示绿色和勾选图标
    const finishIcon = container
      .querySelector('[aria-label="完成"]')
      ?.closest('div');
    expect(finishIcon).toHaveClass('bg-green-500');
  });

  test('renders custom icons', () => {
    const customItems: StepItem[] = [
      {
        title: '自定义图标',
        icon: <span data-testid="custom-icon">🎉</span>,
      },
    ];

    render(<Steps items={customItems} />);

    expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    expect(screen.getByText('🎉')).toBeInTheDocument();
  });

  test('handles disabled steps', () => {
    const onChange = vi.fn();
    const disabledItems: StepItem[] = [
      {
        title: '正常步骤',
      },
      {
        title: '禁用步骤',
        disabled: true,
      },
    ];

    render(<Steps items={disabledItems} clickable onChange={onChange} />);

    // 查找包含禁用步骤的整个步骤项
    const disabledStepItem = screen
      .getByText('禁用步骤')
      .closest('[data-disabled="true"]');
    expect(disabledStepItem).toHaveClass('opacity-50', 'cursor-not-allowed');

    fireEvent.click(disabledStepItem!);
    expect(onChange).not.toHaveBeenCalled();
  });

  test('renders without title and description', () => {
    const minimalItems: StepItem[] = [{ title: '' }, { title: '步骤二' }];

    render(<Steps items={minimalItems} />);

    // 应该只显示图标，不显示内容区域
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('步骤二')).toBeInTheDocument();
  });

  test('applies custom className', () => {
    const { container } = render(
      <Steps items={mockItems} className="custom-steps-class" />,
    );

    expect(container.firstChild).toHaveClass('custom-steps-class');
  });

  test('renders connectors correctly', () => {
    const { container } = render(<Steps items={mockItems} current={1} />);

    // 应该有连接线（除了最后一个步骤）
    const connectors = container.querySelectorAll('[class*="absolute"]');
    expect(connectors.length).toBeGreaterThan(0);
  });

  test('renders finish status with check icon', () => {
    const { container } = render(<Steps items={mockItems} current={2} />);

    // 前两步应该显示勾选图标
    const checkIcons = container.querySelectorAll('[aria-label="完成"]');
    expect(checkIcons.length).toBeGreaterThanOrEqual(1);
  });

  test('renders error status with x icon', () => {
    const errorItems: StepItem[] = [
      {
        title: '错误步骤',
        status: 'error',
      },
    ];

    const { container } = render(<Steps items={errorItems} />);

    // 应该显示错误图标
    const errorIcon = container.querySelector('[aria-label="错误"]');
    expect(errorIcon).toBeInTheDocument();
  });
});
