import type { ReactNode } from 'react';
import { cn } from '@/components/core/class-config';
import classConfig from '@/components/core/components/steps/class-config.ts';

export type StepStatus = 'wait' | 'process' | 'finish' | 'error';
export type StepDirection = 'horizontal' | 'vertical';

export interface StepItem {
  /** 步骤标题 */
  title: ReactNode;
  /** 步骤描述 */
  description?: ReactNode;
  /** 步骤状态，如果不设置，会根据 current 自动推断 */
  status?: StepStatus;
  /** 自定义图标 */
  icon?: ReactNode;
  /** 是否禁用点击 */
  disabled?: boolean;
}

export interface StepsProps {
  /** 步骤数据 */
  items: StepItem[];
  /** 当前步骤，从 0 开始 */
  current?: number;
  /** 步骤条方向 */
  direction?: StepDirection;
  /** 是否可点击切换步骤 */
  clickable?: boolean;
  /** 点击步骤时的回调 */
  onChange?: (current: number) => void;
  /** 自定义 CSS 类名 */
  className?: string;
}

const Steps = ({
  items = [],
  current = 0,
  direction = 'horizontal',
  clickable = false,
  onChange,
  className,
}: StepsProps) => {
  const containerClasses = classConfig.containerConfig({ direction });

  const getStepStatus = (index: number, item: StepItem): StepStatus => {
    if (item.status) {
      return item.status;
    }
    if (index < current) {
      return 'finish';
    }
    if (index === current) {
      return 'process';
    }
    return 'wait';
  };

  const handleStepClick = (index: number, item: StepItem) => {
    if (clickable && !item.disabled && onChange) {
      onChange(index);
    }
  };

  const renderIcon = (index: number, status: StepStatus, icon?: ReactNode) => {
    const iconClasses = classConfig.iconConfig({ status });

    if (icon) {
      return <div className={iconClasses}>{icon}</div>;
    }

    // 默认图标逻辑
    if (status === 'finish') {
      return (
        <div
          className={cn(
            iconClasses,
            // 移除边框，使用纯色圆点
            'h-3 w-3 rounded-full border-0',
          )}
        >
          {/* 空内容，只显示圆点背景 */}
        </div>
      );
    }

    if (status === 'error') {
      return (
        <div className={iconClasses}>
          <svg
            className="h-4 w-4"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
            role="img"
            aria-label="错误"
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      );
    }

    // 默认显示小圆点，类似antd-mobile
    return (
      <div
        className={cn(
          iconClasses,
          // 移除边框，使用纯色圆点
          'h-3 w-3 rounded-full border-0',
          //status === 'process' && 'w-4 h-4', // 当前步骤稍大一些
        )}
      >
        {/* 空内容，只显示圆点背景 */}
      </div>
    );
  };

  if (direction === 'horizontal') {
    return (
      /* <div className={cn(containerClasses, className)}>
        {/!* 图标和连接线行 *!/}
        <div className="flex items-center w-full mb-2">
          {items.map((item, index) => {
            const status = getStepStatus(index, item);
            const isClickable = clickable && !item.disabled;

            return (
              <div key={`icon-${index}`} className="flex items-center flex-1">
                <div
                  className={cn(
                    'flex items-center justify-center',
                    isClickable && 'cursor-pointer',
                    item.disabled && 'cursor-not-allowed opacity-50',
                  )}
                  onClick={() => handleStepClick(index, item)}
                  data-disabled={item.disabled}
                >
                  {renderIcon(index, status, item.icon)}
                </div>

                {/!* 水平连接线 *!/}
                {index < items.length - 1 && (
                  <div className={cn(
                    'flex-1 h-0.5 transition-colors duration-200',
                    status === 'finish' ? 'bg-blue-500' : 'bg-gray-300'
                  )} />
                )}
              </div>
            );
          })}
        </div>

        {/!* 内容行 *!/}
        <div className="flex w-full">
          {items.map((item, index) => {
            const status = getStepStatus(index, item);
            const contentClasses = classConfig.contentConfig({ direction });
            const titleClasses = classConfig.titleConfig({ status });
            const descriptionClasses = classConfig.descriptionConfig({ status });

            return (
              <div key={`content-${index}`} className="flex-1 text-center">
                {(item.title || item.description) && (
                  <div className={contentClasses}>
                    {item.title && <div className={titleClasses}>{item.title}</div>}
                    {item.description && (
                      <div className={descriptionClasses}>{item.description}</div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>*/
      <div className={cn(containerClasses, className)}>
        {items.map((item, index) => {
          const status = getStepStatus(index, item);
          const isClickable = clickable && !item.disabled;

          const contentClasses = classConfig.contentConfig({ direction });
          const titleClasses = classConfig.titleConfig({ status });
          const descriptionClasses = classConfig.descriptionConfig({ status });

          return (
            <div key={index} className="flex flex-1 items-center">
              <div
                className={cn(
                  'flex flex-col items-center text-center',
                  isClickable && 'cursor-pointer',
                  item.disabled && 'cursor-not-allowed opacity-50',
                )}
                onClick={() => handleStepClick(index, item)}
                data-disabled={item.disabled}
              >
                <div
                  className={cn(
                    'h-[24px] w-full transition-colors duration-200 border-t border-blue-500  top-1/2',
                    //status === 'finish' ? 'bg-green-500' : 'bg-gray-300'
                  )}
                >
                  {renderIcon(index, status, item.icon)}
                </div>
                <div className="relative">

                </div>

                {(item.title || item.description) && (
                  <div className={contentClasses}>
                    {item.title && (
                      <div className={titleClasses}>{item.title}</div>
                    )}
                    {item.description && (
                      <div className={descriptionClasses}>
                        {item.description}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* 水平连接线 */}
              {/*{index < items.length - 1 && (
                <div className={cn(
                  'flex-1 h-0.5 mx-4 transition-colors duration-200',
                  status === 'finish' ? 'bg-green-500' : 'bg-gray-300'
                )} />
              )}*/}
            </div>
          );
        })}
      </div>
    );
  }

  // 垂直方向
  return (
    <div className={cn(containerClasses, className)}>
      {items.map((item, index) => {
        const status = getStepStatus(index, item);
        const isClickable = clickable && !item.disabled;

        const contentClasses = classConfig.contentConfig({ direction });
        const titleClasses = classConfig.titleConfig({ status });
        const descriptionClasses = classConfig.descriptionConfig({ status });

        return (
          <div key={index} className="flex">
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  'relative',
                  isClickable && 'cursor-pointer',
                  item.disabled && 'cursor-not-allowed opacity-50',
                )}
                onClick={() => handleStepClick(index, item)}
                data-disabled={item.disabled}
              >
                {renderIcon(index, status, item.icon)}
              </div>

              {/* 垂直连接线 */}
              {index < items.length - 1 && (
                <div
                  className={cn(
                    'my-2 w-0.5 flex-1 transition-colors duration-200',
                    'min-h-[40px]',
                    status === 'finish' ? 'bg-green-500' : 'bg-gray-300',
                  )}
                />
              )}
            </div>

            {(item.title || item.description) && (
              <div className={contentClasses}>
                {item.title && <div className={titleClasses}>{item.title}</div>}
                {item.description && (
                  <div className={descriptionClasses}>{item.description}</div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default Steps;
