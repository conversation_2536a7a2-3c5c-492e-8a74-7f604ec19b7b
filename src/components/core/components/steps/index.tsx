import type { ReactNode } from 'react';
import { cn } from '@/components/core/class-config';
import classConfig from '@/components/core/components/steps/class-config.ts';

export type StepStatus = 'wait' | 'process' | 'finish' | 'error';
export type StepDirection = 'horizontal' | 'vertical';

export interface StepItem {
  /** 步骤标题 */
  title: ReactNode;
  /** 步骤描述 */
  description?: ReactNode;
  /** 步骤状态，如果不设置，会根据 current 自动推断 */
  status?: StepStatus;
  /** 自定义图标 */
  icon?: ReactNode;
  /** 是否禁用点击 */
  disabled?: boolean;
}

export interface StepsProps {
  /** 步骤数据 */
  items: StepItem[];
  /** 当前步骤，从 0 开始 */
  current?: number;
  /** 步骤条方向 */
  direction?: StepDirection;
  /** 是否可点击切换步骤 */
  clickable?: boolean;
  /** 点击步骤时的回调 */
  onChange?: (current: number) => void;
  /** 自定义 CSS 类名 */
  className?: string;
}

const Steps = ({
  items = [],
  current = 0,
  direction = 'horizontal',
  clickable = false,
  onChange,
  className,
}: StepsProps) => {
  const containerClasses = classConfig.containerConfig({ direction });

  const getStepStatus = (index: number, item: StepItem): StepStatus => {
    if (item.status) {
      return item.status;
    }
    if (index < current) {
      return 'finish';
    }
    if (index === current) {
      return 'process';
    }
    return 'wait';
  };

  const handleStepClick = (index: number, item: StepItem) => {
    if (clickable && !item.disabled && onChange) {
      onChange(index);
    }
  };

  const renderIcon = (index: number, status: StepStatus, icon?: ReactNode) => {
    const iconClasses = classConfig.iconConfig({ status });

    if (icon) {
      return <div className={iconClasses}>{icon}</div>;
    }

    // 默认图标逻辑
    if (status === 'finish') {
      return (
        <div className={iconClasses}>
          <svg
            className="h-4 w-4"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
            role="img"
            aria-label="完成"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      );
    }

    if (status === 'error') {
      return (
        <div className={iconClasses}>
          <svg
            className="h-4 w-4"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
            role="img"
            aria-label="错误"
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      );
    }

    // 默认显示步骤数字
    return <div className={iconClasses}>{index + 1}</div>;
  };

  const renderConnector = (index: number, status: StepStatus) => {
    if (index === items.length - 1) return null;

    const connectorClasses = classConfig.connectorConfig({
      direction,
      status:
        status === 'finish'
          ? 'finish'
          : status === 'error'
            ? 'error'
            : undefined,
    });

    return <div className={connectorClasses} />;
  };

  return (
    <div className={cn(containerClasses, className)}>
      {items.map((item, index) => {
        const status = getStepStatus(index, item);
        const isClickable = clickable && !item.disabled;

        const itemClasses = classConfig.itemConfig({
          direction,
          clickable: isClickable,
        });

        const contentClasses = classConfig.contentConfig({ direction });
        const titleClasses = classConfig.titleConfig({ status });
        const descriptionClasses = classConfig.descriptionConfig({ status });

        return (
          <div
            key={index}
            className={cn(
              itemClasses,
              direction === 'horizontal' &&
                index !== items.length - 1 &&
                'flex-1',
              item.disabled && 'cursor-not-allowed opacity-50',
            )}
            onClick={() => handleStepClick(index, item)}
            data-disabled={item.disabled}
          >
            <div className="relative flex items-center">
              {renderIcon(index, status, item.icon)}
              {renderConnector(index, status)}
            </div>

            {(item.title || item.description) && (
              <div className={contentClasses}>
                {item.title && <div className={titleClasses}>{item.title}</div>}
                {item.description && (
                  <div className={descriptionClasses}>{item.description}</div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default Steps;
