import { cva } from 'class-variance-authority';
import { getComponentClassConfig } from '@/components/core/class-config';
import type { defaultConfig } from '@/components/core/class-config/default-config.ts';

const currentConfig = getComponentClassConfig(
  'steps',
) as (typeof defaultConfig)['steps'];

const containerConfig = cva(currentConfig.container.base, {
  variants: {
    direction: currentConfig.container.direction,
  },
});

const itemConfig = cva(currentConfig.item.base, {
  variants: {
    direction: currentConfig.item.direction,
    clickable: {
      true: currentConfig.item.clickable,
    },
  },
});

const iconConfig = cva(currentConfig.icon.base, {
  variants: {
    status: currentConfig.icon.status,
  },
});

const contentConfig = cva(currentConfig.content.base, {
  variants: {
    direction: currentConfig.content.direction,
  },
});

const titleConfig = cva(currentConfig.title.base, {
  variants: {
    status: currentConfig.title.status,
  },
});

const descriptionConfig = cva(currentConfig.description.base, {
  variants: {
    status: currentConfig.description.status,
  },
});

const connectorConfig = cva(currentConfig.connector.base, {
  variants: {
    direction: currentConfig.connector.direction,
    status: currentConfig.connector.status,
  },
});

const classConfig = {
  containerConfig,
  itemConfig,
  iconConfig,
  contentConfig,
  titleConfig,
  descriptionConfig,
  connectorConfig,
};

export default classConfig;
